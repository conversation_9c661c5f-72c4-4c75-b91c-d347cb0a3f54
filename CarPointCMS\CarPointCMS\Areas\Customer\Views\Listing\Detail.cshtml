@using CarPointCMS.Models.Entities
@using CarPointCMS.Models.ViewModels
@model ListingDetailViewModel
@{
    ViewData["Title"] = Model.Listing.ListingName;
    ViewData["Description"] = Model.Listing.ListingDescription?.Substring(0, Math.Min(160, Model.Listing.ListingDescription.Length));
}

<script type='text/javascript' src='https://platform-api.sharethis.com/js/sharethis.js#property=5993ef01e2587a001253a261&product=inline-share-buttons' async='async'></script>

<div class="listing-single-banner" style="background-image: url('~/uploads/listing_featured_photos/@Model.Listing.ListingFeaturedPhoto');">
    <div class="bg"></div>
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <h1>@Model.Listing.ListingName</h1>
                <div class="price">
                    @if(string.IsNullOrEmpty(Model.CurrentCurrencySymbol))
                    {
                        @($"${Model.Listing.ListingPrice:N0}")
                    }
                    else
                    {
                        @($"{Model.CurrentCurrencySymbol}{(Model.Listing.ListingPrice * Model.CurrentCurrencyValue):N0}")
                    }
                </div>
                <div class="location">
                    <i class="fas fa-map-marker-alt"></i> @Model.Listing.ListingLocation.ListingLocationName
                </div>
                <div class="review">
                    @{
                        var overallRating = (double)Model.AverageRating;
                    }
                    @if(overallRating == 5)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    }
                    else if(overallRating == 4.5)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    }
                    else if(overallRating == 4)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    else if(overallRating == 3.5)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                        <i class="far fa-star"></i>
                    }
                    else if(overallRating == 3)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    else if(overallRating == 2.5)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    else if(overallRating == 2)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    else if(overallRating == 1.5)
                    {
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    else if(overallRating == 1)
                    {
                        <i class="fas fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    else if(overallRating == 0)
                    {
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    }
                    <span>(@Model.TotalReviews Reviews)</span>
                </div>
                <div class="call">
                    <i class="fas fa-phone-volume"></i> @Model.Listing.ListingPhone
                </div>
                <div class="listing-items">
                    <a href="@Url.Action("BrandDetail", "Listing", new { slug = Model.Listing.ListingBrand.ListingBrandSlug })">
                        <i class="far fa-edit"></i> @Model.Listing.ListingBrand.ListingBrandName
                    </a>
                    <a href="@Url.Action("AddToWishlist", "Customer", new { id = Model.Listing.Id })">
                        <i class="fas fa-heart"></i> Add to Wishlist
                    </a>
                    <a href="" data-toggle="modal" data-target="#send_message_modal">
                        <i class="far fa-envelope"></i> Send Message
                    </a>

                    <!-- Send Message Modal -->
                    <div class="modal fade modal_listing_detail" id="send_message_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="exampleModalLabel">Send Message</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <form action="@Url.Action("SendMessage", "Customer", new { Area = "Customer" })" method="post">
                                        @Html.AntiForgeryToken()
                                        <input type="hidden" name="ListingId" value="@Model.Listing.Id">
                                        <input type="hidden" name="ListingName" value="@Model.Listing.ListingName">
                                        <input type="hidden" name="ListingSlug" value="@Model.Listing.ListingSlug">
                                        @{
                                            var agentName = Model.Listing.User?.Name ?? Model.Listing.Admin?.Name ?? "";
                                            var agentEmail = Model.Listing.User?.Email ?? Model.Listing.Admin?.Email ?? "";
                                        }
                                        <input type="hidden" name="AgentName" value="@agentName">
                                        <input type="hidden" name="AgentEmail" value="@agentEmail">
                                        <div class="form-group">
                                            <label for="">Name</label>
                                            <div>
                                                <input type="text" name="SenderName" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="">Email</label>
                                            <div>
                                                <input type="email" name="SenderEmail" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="">Phone</label>
                                            <div>
                                                <input type="text" name="SenderPhone" class="form-control">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="">Message</label>
                                            <div>
                                                <textarea name="MessageText" class="form-control h-100" cols="30" rows="10" required></textarea>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div>
                                                <button type="submit" class="btn btn-success">Send Message</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- // Send Message Modal -->

                    <a href="" data-toggle="modal" data-target="#report_modal">
                        <i class="far fa-flag"></i> Report
                    </a>

                    <!-- Report Modal -->
                    <div class="modal fade modal_listing_detail" id="report_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="exampleModalLabel">Submit Report</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <form action="@Url.Action("ReportListing", "Customer", new { Area = "Customer" })" method="post">
                                        @Html.AntiForgeryToken()
                                        <input type="hidden" name="ListingId" value="@Model.Listing.Id">
                                        <input type="hidden" name="ListingName" value="@Model.Listing.ListingName">
                                        <input type="hidden" name="ListingSlug" value="@Model.Listing.ListingSlug">
                                        <div class="form-group">
                                            <label for="">Name</label>
                                            <div>
                                                <input type="text" name="ReporterName" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="">Email</label>
                                            <div>
                                                <input type="email" name="ReporterEmail" class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="">Phone</label>
                                            <div>
                                                <input type="text" name="ReporterPhone" class="form-control">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="">Message</label>
                                            <div>
                                                <textarea name="ReportDescription" class="form-control h-100" cols="30" rows="10" required></textarea>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div>
                                                <button type="submit" class="btn btn-success">Submit Report</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- // Report Modal -->

                </div>

                @if(Model.Listing.ListingSocialItems.Any())
                {
                    <div class="social">
                        <ul>
                            @foreach(var row in Model.Listing.ListingSocialItems)
                            {
                                @{
                                    var iconCode = "";
                                    switch(row.SocialIcon)
                                    {
                                        case "Facebook":
                                            iconCode = "fab fa-facebook-f";
                                            break;
                                        case "Twitter":
                                            iconCode = "fab fa-twitter";
                                            break;
                                        case "LinkedIn":
                                            iconCode = "fab fa-linkedin-in";
                                            break;
                                        case "YouTube":
                                            iconCode = "fab fa-youtube";
                                            break;
                                        case "Pinterest":
                                            iconCode = "fab fa-pinterest-p";
                                            break;
                                        case "GooglePlus":
                                            iconCode = "fab fa-google-plus-g";
                                            break;
                                        case "Instagram":
                                            iconCode = "fab fa-instagram";
                                            break;
                                        default:
                                            iconCode = "fas fa-link";
                                            break;
                                    }
                                }
                                <li>
                                    <a href="@row.SocialUrl"><i class="@iconCode"></i></a>
                                </li>
                            }
                        </ul>
                    </div>
                }

            </div>
        </div>
    </div>
</div>

<div class="page-content">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-md-12 col-sm-12">
                <div class="listing-page">
                    <h2><i class="fas fa-folder"></i> Description</h2>
                    <p>
                        @Html.Raw(Model.Listing.ListingDescription)
                    </p>

                    @if(Model.Listing.ListingPhotos.Any())
                    {
                        <div class="gap"></div>
                        <h2><i class="fas fa-image"></i> Photos</h2>
                        <div class="photo-all">
                            <div class="row">
                                @foreach(var row in Model.Listing.ListingPhotos)
                                {
                                    <div class="col-md-6 col-lg-4">
                                        <div class="item">
                                            <a href="~/uploads/listing_photos/@row.PhotoName" class="magnific">
                                                <img src="~/uploads/listing_photos/@row.PhotoName" alt="">
                                                <div class="icon">
                                                    <i class="fas fa-plus"></i>
                                                </div>
                                                <div class="bg"></div>
                                            </a>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }

                    @if(Model.Listing.ListingVideos.Any())
                    {
                        <div class="gap"></div>
                        <h2><i class="fas fa-video"></i> Videos</h2>
                        <div class="video-all">
                            <div class="row">
                                @foreach(var row in Model.Listing.ListingVideos)
                                {
                                    <div class="col-md-6 col-lg-4">
                                        <div class="item">
                                            <a class="video-button" href="http://www.youtube.com/watch?v=@row.VideoId">
                                                <img src="http://img.youtube.com/vi/@row.VideoId/0.jpg" alt="">
                                                <div class="icon">
                                                    <i class="far fa-play-circle"></i>
                                                </div>
                                                <div class="bg"></div>
                                            </a>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }

                    @if(!string.IsNullOrEmpty(Model.Listing.ListingMap))
                    {
                        <div class="gap"></div>
                        <h2><i class="fas fa-map"></i> Location Map</h2>
                        <div class="map">
                            @Html.Raw(Model.Listing.ListingMap)
                        </div>
                    }

                    <div class="gap"></div>
                    <h2><i class="fas fa-atom"></i> Features</h2>
                    <div class="contact">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tr>
                                    <td class="w-300">Price</td>
                                    <td>
                                        @if(string.IsNullOrEmpty(Model.CurrentCurrencySymbol))
                                        {
                                            @($"${Model.Listing.ListingPrice:N0}")
                                        }
                                        else
                                        {
                                            @($"{Model.CurrentCurrencySymbol}{(Model.Listing.ListingPrice * Model.CurrentCurrencyValue):N0}")
                                        }
                                    </td>
                                </tr>

                                <tr>
                                    <td>Type</td>
                                    <td>
                                        @Model.Listing.ListingType
                                    </td>
                                </tr>

                                @if(!string.IsNullOrEmpty(Model.Listing.ListingExteriorColor))
                                {
                                    <tr>
                                        <td>Exterior Color</td>
                                        <td>
                                            @Model.Listing.ListingExteriorColor
                                        </td>
                                    </tr>
                                }

                                @if(!string.IsNullOrEmpty(Model.Listing.ListingInteriorColor))
                                {
                                    <tr>
                                        <td>Interior Color</td>
                                        <td>
                                            @Model.Listing.ListingInteriorColor
                                        </td>
                                    </tr>
                                }

                                @if(!string.IsNullOrEmpty(Model.Listing.ListingCylinder))
                                {
                                    <tr>
                                        <td>Cylinder</td>
                                        <td>
                                            @Model.Listing.ListingCylinder
                                        </td>
                                    </tr>
                                }

                                @if(!string.IsNullOrEmpty(Model.Listing.ListingFuelType))
                                {
                                    <tr>
                                        <td>Fuel Type</td>
                                        <td>
                                            @Model.Listing.ListingFuelType
                                        </td>
                                    </tr>
                                }

                                @if(!string.IsNullOrEmpty(Model.Listing.ListingTransmission))
                                {
                                    <tr>
                                        <td>Transmission</td>
                                        <td>
                                            @Model.Listing.ListingTransmission
                                        </td>
                                    </tr>
                                }

                                @if(!string.IsNullOrEmpty(Model.Listing.ListingEngineCapacity))
                                {
                                    <tr>
                                        <td>Engine Capacity</td>
                                        <td>
                                            @Model.Listing.ListingEngineCapacity
                                        </td>
                                    </tr>
                                }

                                @if(!string.IsNullOrEmpty(Model.Listing.ListingVin))
                                {
                                    <tr>
                                        <td>VIN</td>
                                        <td>
                                            @Model.Listing.ListingVin
                                        </td>
                                    </tr>
                                }

                                @if(!string.IsNullOrEmpty(Model.Listing.ListingBody))
                                {
                                    <tr>
                                        <td>Body</td>
                                        <td>
                                            @Model.Listing.ListingBody
                                        </td>
                                    </tr>
                                }

                                @if(!string.IsNullOrEmpty(Model.Listing.ListingSeat))
                                {
                                    <tr>
                                        <td>Seat</td>
                                        <td>
                                            @Model.Listing.ListingSeat
                                        </td>
                                    </tr>
                                }

                                @if(!string.IsNullOrEmpty(Model.Listing.ListingWheel))
                                {
                                    <tr>
                                        <td>Wheel</td>
                                        <td>
                                            @Model.Listing.ListingWheel
                                        </td>
                                    </tr>
                                }

                                @if(!string.IsNullOrEmpty(Model.Listing.ListingDoor))
                                {
                                    <tr>
                                        <td>Door</td>
                                        <td>
                                            @Model.Listing.ListingDoor
                                        </td>
                                    </tr>
                                }

                                @if(Model.Listing.ListingMileage.HasValue)
                                {
                                    <tr>
                                        <td>Mileage</td>
                                        <td>
                                            @Model.Listing.ListingMileage
                                        </td>
                                    </tr>
                                }

                                @if(Model.Listing.ListingModelYear.HasValue)
                                {
                                    <tr>
                                        <td>Model Year</td>
                                        <td>
                                            @Model.Listing.ListingModelYear
                                        </td>
                                    </tr>
                                }

                            </table>
                        </div>
                    </div>
                </div>

                @if(Model.Listing.ListingAmenities.Any())
                {
                    <div class="gap"></div>
                    <h2><i class="fas fa-bullhorn"></i> Amenities</h2>
                    <div class="amenities">
                        <ul>
                            @foreach(var row in Model.Listing.ListingAmenities)
                            {
                                <li><i class="fas fa-check-square"></i> @row.Amenity.AmenityName</li>
                            }
                        </ul>
                    </div>
                }

                @if(Model.Listing.ListingAdditionalFeatures.Any())
                {
                    <div class="gap"></div>
                    <h2><i class="far fa-id-card"></i> Additional Features</h2>
                    <div class="contact">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                @foreach(var row in Model.Listing.ListingAdditionalFeatures)
                                {
                                    <tr>
                                        <td class="w-300">@row.AdditionalFeatureName</td>
                                        <td>@row.AdditionalFeatureValue</td>
                                    </tr>
                                }
                            </table>
                        </div>
                    </div>
                }

                <div class="gap"></div>
                <h2><i class="far fa-id-card"></i> Contact Information</h2>
                <div class="contact">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            @if(!string.IsNullOrEmpty(Model.Listing.ListingAddress))
                            {
                                <tr>
                                    <td class="w-200">Address</td>
                                    <td>
                                        @Html.Raw(Model.Listing.ListingAddress.Replace("\n", "<br>"))
                                    </td>
                                </tr>
                            }

                            <tr>
                                <td>Phone Number</td>
                                <td>
                                    @Html.Raw(Model.Listing.ListingPhone.Replace("\n", "<br>"))
                                </td>
                            </tr>

                            @if(!string.IsNullOrEmpty(Model.Listing.ListingEmail))
                            {
                                <tr>
                                    <td>Email Address</td>
                                    <td>
                                        @Html.Raw(Model.Listing.ListingEmail.Replace("\n", "<br>"))
                                    </td>
                                </tr>
                            }

                            @if(!string.IsNullOrEmpty(Model.Listing.ListingWebsite))
                            {
                                <tr>
                                    <td>Website</td>
                                    <td class="website">
                                        <a href="@Model.Listing.ListingWebsite" target="_blank">@Model.Listing.ListingWebsite</a>
                                    </td>
                                </tr>
                            }

                        </table>
                    </div>
                </div>

                <div class="gap"></div>
                <h2>Reviews (@Model.TotalReviews)</h2>

                <div class="review-overall">
                    <div class="review">
                        @{
                            var overallRating = (double)Model.AverageRating;
                        }
                        @if(overallRating == 5)
                        {
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        }
                        else if(overallRating == 4.5)
                        {
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                        }
                        else if(overallRating == 4)
                        {
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="far fa-star"></i>
                        }
                        else if(overallRating == 3.5)
                        {
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                            <i class="far fa-star"></i>
                        }
                        else if(overallRating == 3)
                        {
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                        }
                        else if(overallRating == 2.5)
                        {
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                        }
                        else if(overallRating == 2)
                        {
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                        }
                        else if(overallRating == 1.5)
                        {
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                        }
                        else if(overallRating == 1)
                        {
                            <i class="fas fa-star"></i>
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                        }
                        else if(overallRating == 0)
                        {
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                            <i class="far fa-star"></i>
                        }
                    </div>
                    <div class="total">
                        @if(Model.TotalReviews != 0)
                        {
                            <span>(Overall @overallRating.ToString("F1") out of 5)</span>
                        }
                        else
                        {
                            <span>(Overall 0 out of 5)</span>
                        }
                    </div>
                </div>

                <div class="reviews">
                    @if(!Model.Reviews.Any())
                    {
                        <span class="text-danger">No Review Found</span>
                    }
                    else
                    {
                        @foreach(var item in Model.Reviews)
                        {
                            @{
                                var userDetail = item.AgentType == "User" ? item.User : (object)item.Admin;
                                var userName = item.AgentType == "User" ? item.User?.Name : item.Admin?.Name;
                                var userPhoto = item.AgentType == "User" ? item.User?.Photo : item.Admin?.Photo;
                                var userCreatedAt = item.AgentType == "User" ? item.User?.CreatedAt : item.Admin?.CreatedAt;
                            }
                            <div class="row item">
                                <div class="col-md-12 col-lg-2">
                                    <div class="photo">
                                        @if(string.IsNullOrEmpty(userPhoto))
                                        {
                                            <img src="~/uploads/user_photos/default_photo.jpg" alt="">
                                        }
                                        else
                                        {
                                            <img src="~/uploads/user_photos/@userPhoto" alt="">
                                        }
                                    </div>
                                </div>
                                <div class="col-md-12 col-lg-10">
                                    <div class="name">
                                        @userName
                                    </div>
                                    <div class="date-time">
                                        @(userCreatedAt?.ToString("dd MMM, yyyy") ?? "")
                                    </div>

                                    <div class="score">
                                        @if(item.Rating == 5)
                                        {
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        }
                                        else if(item.Rating == 4)
                                        {
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                        }
                                        else if(item.Rating == 3)
                                        {
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                            <i class="far fa-star"></i>
                                        }
                                        else if(item.Rating == 2)
                                        {
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                            <i class="far fa-star"></i>
                                            <i class="far fa-star"></i>
                                        }
                                        else if(item.Rating == 1)
                                        {
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                            <i class="far fa-star"></i>
                                            <i class="far fa-star"></i>
                                            <i class="far fa-star"></i>
                                        }
                                    </div>
                                    <div class="comment">
                                        <p>
                                            @Html.Raw(item.ReviewText)
                                        </p>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                </div>

                <div class="gap"></div>
                <h2>Write a Review</h2>
                <div class="review-form">
                    @if(!User.Identity.IsAuthenticated)
                    {
                        <a href="@Url.Action("Login", "Account", new { Area = "Customer" })" class="login-to-review">Login to Review</a>
                    }
                    else
                    {
                        @{
                            var currentUserId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                            var agentId = Model.Listing.User?.Id.ToString() ?? Model.Listing.Admin?.Id.ToString() ?? "";
                            var alreadyReviewed = Model.Reviews.Any(r => r.AgentId.ToString() == currentUserId && r.AgentType == "User");
                        }

                        @if(currentUserId == agentId)
                        {
                            <div class="text-danger">You cannot review your own product.</div>
                        }
                        else if(alreadyReviewed)
                        {
                            <div class="text-danger">You have already given a review for this listing.</div>
                        }
                        else
                        {
                            <form action="@Url.Action("SubmitReview", "Customer", new { Area = "Customer" })" method="post">
                                @Html.AntiForgeryToken()
                                <input type="hidden" name="ListingId" value="@Model.Listing.Id">
                                <div class="form-group">
                                    <label for="">Your Rating</label>
                                    <select name="Rating" class="form-control">
                                        <option value="1">1 Star</option>
                                        <option value="2">2 Stars</option>
                                        <option value="3">3 Stars</option>
                                        <option value="4">4 Stars</option>
                                        <option value="5">5 Stars</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="">Your Review</label>
                                    <textarea name="ReviewText" class="form-control h-100" cols="30" rows="10"></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">Submit</button>
                            </form>
                        }
                    }
                </div>

            </div>
        </div>
        <div class="col-lg-4 col-md-12 col-sm-12">
            <div class="listing-sidebar" id="sticky_sidebar">

                <div class="ls-widget">
                    <h2>Agent</h2>
                    <div class="agent">
                        <div class="photo">
                            @{
                                var agentDetail = Model.Listing.User ?? (object)Model.Listing.Admin;
                                var agentPhoto = Model.Listing.User?.Photo ?? Model.Listing.Admin?.Photo;
                                var agentName = Model.Listing.User?.Name ?? Model.Listing.Admin?.Name;
                                var agentType = Model.Listing.User != null ? "user" : "admin";
                                var agentId = Model.Listing.User?.Id ?? Model.Listing.Admin?.Id ?? 0;
                            }
                            @if(string.IsNullOrEmpty(agentPhoto))
                            {
                                <img src="~/uploads/user_photos/default_photo.jpg" alt="">
                            }
                            else
                            {
                                <img src="~/uploads/user_photos/@agentPhoto" alt="">
                            }
                        </div>
                        <div class="text">
                            <h3><a href="@Url.Action("AgentDetail", "Listing", new { type = agentType, id = agentId })">@agentName</a></h3>
                            <h4>Posted on @Model.Listing.CreatedAt.ToString("dd MMM, yyyy")</h4>
                        </div>
                    </div>
                    <div class="agent-contact">
                        <ul>
                            @{
                                var agentAddress = Model.Listing.User?.Address ?? Model.Listing.Admin?.Address ?? "";
                                var agentCity = Model.Listing.User?.City ?? "";
                                var agentState = Model.Listing.User?.State ?? "";
                                var agentCountry = Model.Listing.User?.Country ?? Model.Listing.Admin?.Country ?? "";
                                var agentPhone = Model.Listing.User?.Phone ?? Model.Listing.Admin?.Phone ?? "";
                                var agentEmail = Model.Listing.User?.Email ?? Model.Listing.Admin?.Email ?? "";
                                var agentWebsite = Model.Listing.User?.Website ?? Model.Listing.Admin?.Website ?? "";
                            }
                            @if(!string.IsNullOrEmpty(agentAddress) || !string.IsNullOrEmpty(agentCity) || !string.IsNullOrEmpty(agentState) || !string.IsNullOrEmpty(agentCountry))
                            {
                                <li>
                                    <i class="fas fa-map-marker-alt"></i> @agentAddress @agentCity @agentCountry
                                </li>
                            }
                            @if(!string.IsNullOrEmpty(agentPhone))
                            {
                                <li><i class="fas fa-phone-volume"></i> @agentPhone</li>
                            }
                            @if(!string.IsNullOrEmpty(agentEmail))
                            {
                                <li><i class="fas fa-envelope"></i> @agentEmail</li>
                            }
                            @if(!string.IsNullOrEmpty(agentWebsite))
                            {
                                <li><a href="@agentWebsite" target="_blank"><i class="fas fa-globe"></i> @agentWebsite</a></li>
                            }
                        </ul>
                    </div>

                    @{
                        var agentFacebook = Model.Listing.User?.Facebook ?? Model.Listing.Admin?.Facebook ?? "";
                        var agentTwitter = Model.Listing.User?.Twitter ?? Model.Listing.Admin?.Twitter ?? "";
                        var agentLinkedin = Model.Listing.User?.Linkedin ?? Model.Listing.Admin?.Linkedin ?? "";
                        var agentPinterest = Model.Listing.User?.Pinterest ?? "";
                        var agentYoutube = Model.Listing.User?.Youtube ?? Model.Listing.Admin?.Youtube ?? "";
                    }
                    @if(!string.IsNullOrEmpty(agentFacebook) || !string.IsNullOrEmpty(agentTwitter) || !string.IsNullOrEmpty(agentLinkedin) || !string.IsNullOrEmpty(agentPinterest) || !string.IsNullOrEmpty(agentYoutube))
                    {
                        <div class="agent-social">
                            <ul>
                                @if(!string.IsNullOrEmpty(agentFacebook))
                                {
                                    <li><a href="@agentFacebook" target="_blank"><i class="fab fa-facebook-f"></i></a></li>
                                }

                                @if(!string.IsNullOrEmpty(agentTwitter))
                                {
                                    <li><a href="@agentTwitter" target="_blank"><i class="fab fa-twitter"></i></a></li>
                                }

                                @if(!string.IsNullOrEmpty(agentLinkedin))
                                {
                                    <li><a href="@agentLinkedin" target="_blank"><i class="fab fa-linkedin-in"></i></a></li>
                                }

                                @if(!string.IsNullOrEmpty(agentPinterest))
                                {
                                    <li><a href="@agentPinterest" target="_blank"><i class="fab fa-pinterest-p"></i></a></li>
                                }

                                @if(!string.IsNullOrEmpty(agentYoutube))
                                {
                                    <li><a href="@agentYoutube" target="_blank"><i class="fab fa-youtube"></i></a></li>
                                }
                            </ul>
                        </div>
                    }

                    <a href="@Url.Action("AgentDetail", "Listing", new { type = agentType, id = agentId })" class="btn btn-primary btn-block agent-view-profile">View Profile</a>
                </div>

                @if(!string.IsNullOrEmpty(Model.Listing.ListingOhMonday) || !string.IsNullOrEmpty(Model.Listing.ListingOhTuesday) || !string.IsNullOrEmpty(Model.Listing.ListingOhWednesday) || !string.IsNullOrEmpty(Model.Listing.ListingOhThursday) || !string.IsNullOrEmpty(Model.Listing.ListingOhFriday) || !string.IsNullOrEmpty(Model.Listing.ListingOhSaturday) || !string.IsNullOrEmpty(Model.Listing.ListingOhSunday))
                {
                    <div class="ls-widget">
                        <h2>Opening Hour</h2>
                        <div class="openning-hour">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tr>
                                        <td>Monday</td>
                                        <td>@Model.Listing.ListingOhMonday</td>
                                    </tr>
                                    <tr>
                                        <td>Tuesday</td>
                                        <td>@Model.Listing.ListingOhTuesday</td>
                                    </tr>
                                    <tr>
                                        <td>Wednesday</td>
                                        <td>@Model.Listing.ListingOhWednesday</td>
                                    </tr>
                                    <tr>
                                        <td>Thursday</td>
                                        <td>@Model.Listing.ListingOhThursday</td>
                                    </tr>
                                    <tr>
                                        <td>Friday</td>
                                        <td>@Model.Listing.ListingOhFriday</td>
                                    </tr>
                                    <tr>
                                        <td>Saturday</td>
                                        <td>@Model.Listing.ListingOhSaturday</td>
                                    </tr>
                                    <tr>
                                        <td>Sunday</td>
                                        <td>@Model.Listing.ListingOhSunday</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                }

                <div class="ls-widget">
                    <h2>Brands</h2>
                    <div class="category">
                        <ul>
                            @foreach(var row in Model.ListingBrands)
                            {
                                <li><a href="@Url.Action("BrandDetail", "Listing", new { slug = row.ListingBrandSlug })"><i class="fas fa-angle-right"></i> @row.ListingBrandName</a></li>
                            }
                        </ul>
                    </div>
                </div>

                <div class="ls-widget">
                    <h2>Locations</h2>
                    <div class="category">
                        <ul>
                            @foreach(var row in Model.ListingLocations)
                            {
                                <li><a href="@Url.Action("LocationDetail", "Listing", new { slug = row.ListingLocationSlug })"><i class="fas fa-angle-right"></i> @row.ListingLocationName</a></li>
                            }
                        </ul>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Photo gallery magnific popup
        $('.magnific').magnificPopup({
            type: 'image',
            gallery: {
                enabled: true
            }
        });

        // Video popup
        $('.video-button').magnificPopup({
            type: 'iframe',
            iframe: {
                patterns: {
                    youtube: {
                        index: 'youtube.com/',
                        id: 'v=',
                        src: 'https://www.youtube.com/embed/%id%?autoplay=1'
                    }
                },
                srcAction: 'iframe_src'
            }
        });

        // Sticky sidebar
        if ($("#sticky_sidebar").length) {
            $("#sticky_sidebar").sticky({
                topSpacing: 70
            });
        }
    });
</script>
