using CarPointCMS.Models.Entities;

namespace CarPointCMS.Models.ViewModels
{
    public class ListingDetailViewModel
    {
        public Listing Listing { get; set; } = new();
        public List<Review> Reviews { get; set; } = new();
        public List<ListingPhoto> ListingPhotos { get; set; } = new();
        public List<ListingVideo> ListingVideos { get; set; } = new();
        public List<ListingAmenity> ListingAmenities { get; set; } = new();
        public List<ListingAdditionalFeature> ListingAdditionalFeatures { get; set; } = new();
        public List<ListingSocialItem> ListingSocialItems { get; set; } = new();
        public User? AgentDetail { get; set; }
        public GeneralSetting GeneralSettings { get; set; } = new();
        public string? CurrentCurrencyName { get; set; }
        public string? CurrentCurrencySymbol { get; set; }
        public decimal CurrentCurrencyValue { get; set; } = 1;
        public List<Listing> RelatedListings { get; set; } = new();
        public List<ListingBrand> ListingBrands { get; set; } = new();
        public List<ListingLocation> ListingLocations { get; set; } = new();

        // Rating properties
        public decimal AverageRating { get; set; }
        public int TotalReviews { get; set; }
    }
}
