using CarPointCMS.Models.Entities;

namespace CarPointCMS.Models.ViewModels
{
    public class ListingDetailViewModel
    {
        public Listing Listing { get; set; } = new();
        public List<Review> Reviews { get; set; } = new();
        public List<ListingPhoto> ListingPhotos { get; set; } = new();
        public List<ListingVideo> ListingVideos { get; set; } = new();
        public List<ListingAmenity> ListingAmenities { get; set; } = new();
        public List<ListingAdditionalFeature> ListingAdditionalFeatures { get; set; } = new();
        public List<ListingSocialItem> ListingSocialItems { get; set; } = new();
        public User? AgentDetail { get; set; }
        public GeneralSetting GeneralSettings { get; set; } = new();
        public string? CurrentCurrencyName { get; set; }
        public string? CurrentCurrencySymbol { get; set; }
        public decimal CurrentCurrencyValue { get; set; } = 1;
        public List<Listing> RelatedListings { get; set; } = new();
        public List<ListingBrand> ListingBrands { get; set; } = new();
        public List<ListingLocation> ListingLocations { get; set; } = new();

        // Rating properties
        public decimal AverageRating { get; set; }
        public int TotalReviews { get; set; }
    }

    public class AgentDetailViewModel
    {
        public User? User { get; set; }
        public Admin? Admin { get; set; }
        public List<Listing> Listings { get; set; } = new();
        public List<Review> Reviews { get; set; } = new();
        public GeneralSetting GeneralSettings { get; set; } = new();
        public string? CurrentCurrencyName { get; set; }
        public string? CurrentCurrencySymbol { get; set; }
        public decimal CurrentCurrencyValue { get; set; } = 1;
        public string AgentType { get; set; } = string.Empty;
        public int AgentId { get; set; }

        // Helper properties for unified access
        public string? AgentName => User?.Name ?? Admin?.Name;
        public string? AgentPhoto => User?.Photo ?? Admin?.Photo;
        public string? AgentBanner => User?.Banner ?? Admin?.Banner;
        public string? AgentEmail => User?.Email ?? Admin?.Email;
        public string? AgentPhone => User?.Phone ?? Admin?.Phone;
        public string? AgentAddress => User?.Address ?? Admin?.Address;
        public string? AgentWebsite => User?.Website ?? Admin?.Website;
        public string? AgentFacebook => User?.Facebook ?? Admin?.Facebook;
        public string? AgentTwitter => User?.Twitter ?? Admin?.Twitter;
        public string? AgentLinkedIn => User?.LinkedIn ?? Admin?.LinkedIn;
        public string? AgentPinterest => User?.Pinterest ?? Admin?.Pinterest;
        public string? AgentYoutube => User?.Youtube ?? Admin?.Youtube;
        public DateTime? AgentCreatedAt => User?.CreatedAt ?? Admin?.CreatedAt;
    }
}
