@using CarPointCMS.Models.ViewModels
@using CarPointCMS.Models.Entities
@model HomeViewModel
@{
    ViewData["Title"] = Model.PageHome.SeoTitle ?? "Home";
    ViewData["Description"] = Model.PageHome.SeoMetaDescription ?? "";
}

<div class="search-section" style="background-image:url('~/uploads/site_photos/@Model.PageHome.SearchBackground');">
    <div class="bg"></div>
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <h1>@Model.PageHome.SearchHeading</h1>
                <p>
                    @Model.PageHome.SearchText
                </p>
                <div class="box">
                    <form action="@Url.Action("Index", "Listing", new { area = "Customer" })" method="GET">
                        <div class="input-group input-box mb-3">
                            <input type="text" class="form-control" placeholder="Find Anything" name="searchText">
                            <select name="locationId" class="form-control select2">
                                <option value="">Select Location</option>
                                @foreach (var row in Model.ListingLocations)
                                {
                                    <option value="@row.Location.Id">@row.Location.ListingLocationName</option>
                                }
                            </select>
                            <select name="brandId" class="form-control select2">
                                <option value="">Select Brand</option>
                                @foreach (var row in Model.ListingBrands)
                                {
                                    <option value="@row.Brand.Id">@row.Brand.ListingBrandName</option>
                                }
                            </select>
                            <select name="listing_type" class="form-control select2">
                                <option value="">Select Type</option>
                                <option value="New Car">New Car</option>
                                <option value="Used Car">Used Car</option>
                            </select>
                            <div class="input-group-append">
                                <button type="submit"><i class="fa fa-search"></i> Search</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@* Advertisement Section Above Brands *@
@if (Model.HomeAdvertisement.AboveBrandStatus == "Show")
{
    <div class="ad-section">
        <div class="container">
            <div class="row">
                <div class="col-md-6 col-sm-12 wow fadeInUp">
                    <div class="inner">
                        @if (string.IsNullOrEmpty(Model.HomeAdvertisement.AboveBrand1Url))
                        {
                            <img src="~/uploads/advertisements/@Model.HomeAdvertisement.AboveBrand1" alt="">
                        }
                        else
                        {
                            <a href="@Model.HomeAdvertisement.AboveBrand1Url" target="_blank"><img src="~/uploads/advertisements/@Model.HomeAdvertisement.AboveBrand1" alt=""></a>
                        }
                    </div>
                </div>
                <div class="col-md-6 col-sm-12 wow fadeInUp">
                    <div class="inner">
                        @if (string.IsNullOrEmpty(Model.HomeAdvertisement.AboveBrand2Url))
                        {
                            <img src="~/uploads/advertisements/@Model.HomeAdvertisement.AboveBrand2" alt="">
                        }
                        else
                        {
                            <a href="@Model.HomeAdvertisement.AboveBrand2Url" target="_blank"><img src="~/uploads/advertisements/@Model.HomeAdvertisement.AboveBrand2" alt=""></a>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@* Brands Section *@
@if (Model.PageHome.BrandStatus == "Show")
{
    <div class="popular-city">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="heading">
                        <h2>@Model.PageHome.BrandHeading</h2>
                        <h3>@Model.PageHome.BrandSubheading</h3>
                    </div>
                </div>
            </div>
            <div class="row">
                @{
                    var brandCount = 0;
                }
                @foreach (var row in Model.ListingBrands)
                {
                    brandCount++;
                    if (brandCount > Model.PageHome.BrandTotal)
                    {
                        break;
                    }
                    <div class="col-lg-3 col-md-6 col-sm-6 wow fadeInUp">
                        <div class="popular-city-item effect-item">
                            <div class="photo image-effect">
                                <img src="~/uploads/listing_brand_photos/@row.Brand.ListingBrandPhoto" alt="">
                            </div>
                            <div class="text">
                                <h4>@row.Brand.ListingBrandName</h4>
                                <p>@row.ListingCount Items</p>
                            </div>
                            <a href="@Url.Action("BrandDetail", "Listing", new { area = "Customer", slug = row.Brand.ListingBrandSlug })"></a>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}

@* Advertisement Section Above Featured Listings *@
@if (Model.HomeAdvertisement.AboveFeaturedListingStatus == "Show")
{
    <div class="ad-section">
        <div class="container">
            <div class="row">
                <div class="col-md-6 col-sm-12 wow fadeInUp">
                    <div class="inner">
                        @if (string.IsNullOrEmpty(Model.HomeAdvertisement.AboveFeaturedListing1Url))
                        {
                            <img src="~/uploads/advertisements/@Model.HomeAdvertisement.AboveFeaturedListing1" alt="">
                        }
                        else
                        {
                            <a href="@Model.HomeAdvertisement.AboveFeaturedListing1Url" target="_blank"><img src="~/uploads/advertisements/@Model.HomeAdvertisement.AboveFeaturedListing1" alt=""></a>
                        }
                    </div>
                </div>
                <div class="col-md-6 col-sm-12 wow fadeInUp">
                    <div class="inner">
                        @if (string.IsNullOrEmpty(Model.HomeAdvertisement.AboveFeaturedListing2Url))
                        {
                            <img src="~/uploads/advertisements/@Model.HomeAdvertisement.AboveFeaturedListing2" alt="">
                        }
                        else
                        {
                            <a href="@Model.HomeAdvertisement.AboveFeaturedListing2Url" target="_blank"><img src="~/uploads/advertisements/@Model.HomeAdvertisement.AboveFeaturedListing2" alt=""></a>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@* Video Section *@
@if (Model.PageHome.VideoStatus == "Show")
{
    <div class="home-video" style="background-image: url(~/uploads/site_photos/@Model.PageHome.VideoBackground)">
        <div class="bg"></div>
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h2>@Model.PageHome.VideoHeading</h2>
                    <p>
                        @Html.Raw(Model.PageHome.VideoSubheading?.Replace("\n", "<br>"))
                    </p>
                    <div class="video-section">
                        <a class="video-button" href="http://www.youtube.com/watch?v=@Model.PageHome.VideoYoutubeId"><i class="far fa-play-circle"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@* Featured Listings Section *@
@if (Model.PageHome.ListingStatus == "Show")
{
    <div class="listing">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="heading">
                        <h2>@Model.PageHome.ListingHeading</h2>
                        <h3>@Model.PageHome.ListingSubheading</h3>
                    </div>
                </div>
            </div>
            <div class="row">
                @{
                    var listingCount = 0;
                }
                @foreach (var row in Model.FeaturedListings)
                {
                    listingCount++;
                    if (listingCount > Model.PageHome.ListingTotal)
                    {
                        break;
                    }

                    string fadeClass = "";
                    if (listingCount % 3 == 0)
                        fadeClass = "fadeInRight";
                    else if ((listingCount - 1) % 3 == 0)
                        fadeClass = "fadeInLeft";
                    else
                        fadeClass = "fadeInUp";

                    <div class="col-lg-4 col-md-6 col-sm-12 wow @fadeClass">
                        <div class="listing-item effect-item">
                            <div class="photo image-effect">
                                <a href="@Url.Action("Detail", "Listing", new { area = "Customer", id = row.Id, slug = row.ListingSlug })">
                                    <img src="~/uploads/listing_featured_photos/@row.ListingFeaturedPhoto" alt="">
                                </a>
                                <div class="brand">
                                    <a href="@Url.Action("BrandDetail", "Listing", new { area = "Customer", slug = row.ListingBrand.ListingBrandSlug })">@row.ListingBrand.ListingBrandName</a>
                                </div>
                                <div class="wishlist">
                                    <a href="@Url.Action("AddWishlist", "Customer", new { area = "Customer", id = row.Id })"><i class="fas fa-heart"></i></a>
                                </div>
                                <div class="featured-text">Featured</div>
                            </div>
                            <div class="text">
                                <div class="type-price">
                                    <div class="type">
                                        @if (row.ListingType == "New Car")
                                        {
                                            <div class="inner-new">New Car</div>
                                        }
                                        else
                                        {
                                            <div class="inner-used">Used Car</div>
                                        }
                                    </div>
                                    <div class="price">
                                        @if (string.IsNullOrEmpty(Model.CurrentCurrencySymbol))
                                        {
                                            @($"${row.ListingPrice:N0}")
                                        }
                                        else
                                        {
                                            @($"{Model.CurrentCurrencySymbol}{(row.ListingPrice * Model.CurrentCurrencyValue):N0}")
                                        }
                                    </div>
                                </div>

                                <h3><a href="@Url.Action("Detail", "Listing", new { area = "Customer", id = row.Id, slug = row.ListingSlug })">@row.ListingName</a></h3>
                                <div class="location">
                                    <i class="fas fa-map-marker-alt"></i> @row.ListingLocation.ListingLocationName
                                </div>

                                @* Rating Section *@
                                @{
                                    var reviews = row.Reviews ?? new List<Review>();
                                    var reviewCount = reviews.Count;
                                    var overallRating = 0.0;

                                    if (reviewCount > 0)
                                    {
                                        var totalRating = reviews.Sum(r => r.Rating);
                                        overallRating = (double)totalRating / reviewCount;

                                        // Round to nearest 0.5
                                        if (overallRating > 0 && overallRating <= 1)
                                            overallRating = 1;
                                        else if (overallRating > 1 && overallRating <= 1.5)
                                            overallRating = 1.5;
                                        else if (overallRating > 1.5 && overallRating <= 2)
                                            overallRating = 2;
                                        else if (overallRating > 2 && overallRating <= 2.5)
                                            overallRating = 2.5;
                                        else if (overallRating > 2.5 && overallRating <= 3)
                                            overallRating = 3;
                                        else if (overallRating > 3 && overallRating <= 3.5)
                                            overallRating = 3.5;
                                        else if (overallRating > 3.5 && overallRating <= 4)
                                            overallRating = 4;
                                        else if (overallRating > 4 && overallRating <= 4.5)
                                            overallRating = 4.5;
                                        else if (overallRating > 4.5 && overallRating <= 5)
                                            overallRating = 5;
                                    }
                                }

                                <div class="review">
                                    @if (overallRating == 5)
                                    {
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    }
                                    else if (overallRating == 4.5)
                                    {
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                    }
                                    else if (overallRating == 4)
                                    {
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                    }
                                    else if (overallRating == 3.5)
                                    {
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                        <i class="far fa-star"></i>
                                    }
                                    else if (overallRating == 3)
                                    {
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                    }
                                    else if (overallRating == 2.5)
                                    {
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                    }
                                    else if (overallRating == 2)
                                    {
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                    }
                                    else if (overallRating == 1.5)
                                    {
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                    }
                                    else if (overallRating == 1)
                                    {
                                        <i class="fas fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                    }
                                    else
                                    {
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                        <i class="far fa-star"></i>
                                    }
                                    <span>(@reviewCount Reviews)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}

@* Testimonials Section *@
@if (Model.PageHome.TestimonialStatus == "Show")
{
    <div class="testimonial" style="background-image:url('~/uploads/site_photos/@Model.PageHome.TestimonialBackground');">
        <div class="testimonial-bg"></div>
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="heading">
                        <h2>@Model.PageHome.TestimonialHeading</h2>
                        <h3>@Model.PageHome.TestimonialSubheading</h3>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="testimonial-carousel owl-carousel">
                        @foreach (var row in Model.Testimonials)
                        {
                            <div class="testimonial-item wow fadeInUp">
                                <div class="photo">
                                    <img src="~/uploads/testimonials/@row.PersonPhoto" alt="">
                                </div>
                                <div class="text">
                                    <p>
                                        @Html.Raw(row.TestimonialText?.Replace("\n", "<br>"))
                                    </p>
                                    <h3>@row.PersonName</h3>
                                    <h4>@row.PersonDesignation</h4>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@* Advertisement Section Above Locations *@
@if (Model.HomeAdvertisement.AboveLocationStatus == "Show")
{
    <div class="ad-section">
        <div class="container">
            <div class="row">
                <div class="col-md-6 col-sm-12 wow fadeInUp">
                    <div class="inner">
                        @if (string.IsNullOrEmpty(Model.HomeAdvertisement.AboveLocation1Url))
                        {
                            <img src="~/uploads/advertisements/@Model.HomeAdvertisement.AboveLocation1" alt="">
                        }
                        else
                        {
                            <a href="@Model.HomeAdvertisement.AboveLocation1Url" target="_blank"><img src="~/uploads/advertisements/@Model.HomeAdvertisement.AboveLocation1" alt=""></a>
                        }
                    </div>
                </div>
                <div class="col-md-6 col-sm-12 wow fadeInUp">
                    <div class="inner">
                        @if (string.IsNullOrEmpty(Model.HomeAdvertisement.AboveLocation2Url))
                        {
                            <img src="~/uploads/advertisements/@Model.HomeAdvertisement.AboveLocation2" alt="">
                        }
                        else
                        {
                            <a href="@Model.HomeAdvertisement.AboveLocation2Url" target="_blank"><img src="~/uploads/advertisements/@Model.HomeAdvertisement.AboveLocation2" alt=""></a>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@* Locations Section *@
@if (Model.PageHome.LocationStatus == "Show")
{
    <div class="popular-city">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="heading">
                        <h2>@Model.PageHome.LocationHeading</h2>
                        <h3>@Model.PageHome.LocationSubheading</h3>
                    </div>
                </div>
            </div>
            <div class="row">
                @{
                    var locationCount = 0;
                }
                @foreach (var row in Model.ListingLocations)
                {
                    locationCount++;
                    if (locationCount > Model.PageHome.LocationTotal)
                    {
                        break;
                    }
                    <div class="col-lg-3 col-md-6 col-sm-6 wow fadeInUp">
                        <div class="popular-city-item effect-item">
                            <div class="photo image-effect">
                                <img src="~/uploads/listing_location_photos/@row.Location.ListingLocationPhoto" alt="">
                            </div>
                            <div class="text">
                                <h4>@row.Location.ListingLocationName</h4>
                                <p>@row.ListingCount Listings</p>
                            </div>
                            <a href="@Url.Action("LocationDetail", "Listing", new { area = "Customer", slug = row.Location.ListingLocationSlug })"></a>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}