@model AgentDetailViewModel
@{
    ViewData["Title"] = "Agent Detail - " + Model.AgentName;
}

@{
    var banner = !string.IsNullOrEmpty(Model.AgentBanner) ? Model.AgentBanner : "default_banner.jpg";
}

<div class="agent-banner" style="background-image: url('~/uploads/user_photos/@banner');">
    <div class="bg"></div>
    <div class="container">
        <div class="row">
            <div class="col-lg-6 col-md-12">
                <div class="agent">
                    <div class="photo">
                        @if(string.IsNullOrEmpty(Model.AgentPhoto))
                        {
                            <img src="~/uploads/user_photos/default_photo.jpg" alt="">
                        }
                        else
                        {
                            <img src="~/uploads/user_photos/@Model.AgentPhoto" alt="">
                        }
                    </div>
                    <div class="text">
                        <h3>@Model.AgentName</h3>
                        <h4>@ViewBag.RegisteredOn @Model.AgentCreatedAt?.ToString("dd MMM, yyyy")</h4>
                    </div>
                </div>

                @{
                    var hasSocialMedia = !string.IsNullOrEmpty(Model.AgentFacebook) ||
                                       !string.IsNullOrEmpty(Model.AgentTwitter) ||
                                       !string.IsNullOrEmpty(Model.AgentLinkedIn) ||
                                       !string.IsNullOrEmpty(Model.AgentPinterest) ||
                                       !string.IsNullOrEmpty(Model.AgentYoutube);
                }

                @if(hasSocialMedia)
                {
                    <div class="social">
                        <ul>
                            @if(!string.IsNullOrEmpty(Model.AgentFacebook))
                            {
                                <li><a href="@Model.AgentFacebook" target="_blank"><i class="fab fa-facebook-f"></i></a></li>
                            }

                            @if(!string.IsNullOrEmpty(Model.AgentTwitter))
                            {
                                <li><a href="@Model.AgentTwitter" target="_blank"><i class="fab fa-twitter"></i></a></li>
                            }

                            @if(!string.IsNullOrEmpty(Model.AgentLinkedIn))
                            {
                                <li><a href="@Model.AgentLinkedIn" target="_blank"><i class="fab fa-linkedin-in"></i></a></li>
                            }

                            @if(!string.IsNullOrEmpty(Model.AgentPinterest))
                            {
                                <li><a href="@Model.AgentPinterest" target="_blank"><i class="fab fa-pinterest-p"></i></a></li>
                            }

                            @if(!string.IsNullOrEmpty(Model.AgentYoutube))
                            {
                                <li><a href="@Model.AgentYoutube" target="_blank"><i class="fab fa-youtube"></i></a></li>
                            }
                        </ul>
                    </div>
                }
            </div>
            <div class="col-lg-6 col-md-12">
                <div class="contact">
                    @if(!string.IsNullOrEmpty(Model.AgentAddress))
                    {
                        <div class="item"><i class="fas fa-map-marker-alt"></i> @Model.AgentAddress</div>
                    }

                    @if(!string.IsNullOrEmpty(Model.AgentPhone))
                    {
                        <div class="item"><i class="fas fa-phone-volume"></i> @Model.AgentPhone</div>
                    }

                    @if(!string.IsNullOrEmpty(Model.AgentEmail))
                    {
                        <div class="item"><i class="fas fa-envelope"></i> @Model.AgentEmail</div>
                    }

                    @if(!string.IsNullOrEmpty(Model.AgentWebsite))
                    {
                        <div class="item"><i class="fas fa-globe"></i> @Model.AgentWebsite</div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<div class="page-content">
    <div class="container">
        <div class="row listing pb_0">

            @foreach(var listing in Model.Listings)
            {
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <div class="listing-item">
                        <div class="photo">
                            <a href="@Url.Action("Detail", "Listing", new { area = "Customer", id = listing.Id })"><img src="~/uploads/listing_featured_photos/@listing.ListingFeaturedPhoto" alt=""></a>
                            <div class="brand">
                                <a href="@Url.Action("BrandDetail", "Listing", new { area = "Customer", id = listing.ListingBrandId })">@listing.ListingBrand.ListingBrandName</a>
                            </div>
                            <div class="wishlist">
                                <a href="@Url.Action("AddWishlist", "Home", new { area = "Customer", id = listing.Id })"><i class="fas fa-heart"></i></a>
                            </div>
                            @if(listing.IsFeatured)
                            {
                                <div class="featured-text">@ViewBag.Featured</div>
                            }
                        </div>
                        <div class="text">

                            <div class="type-price">
                                <div class="type">
                                    @if(listing.ListingType == "New Car")
                                    {
                                        <div class="inner-new">
                                            @ViewBag.NewCar
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="inner-used">
                                            @ViewBag.UsedCar
                                        </div>
                                    }
                                </div>
                                <div class="price">
                                    @if(string.IsNullOrEmpty(Model.CurrentCurrencySymbol))
                                    {
                                        @($"${listing.ListingPrice:N0}")
                                    }
                                    else
                                    {
                                        @($"{Model.CurrentCurrencySymbol}{(listing.ListingPrice * Model.CurrentCurrencyValue):N0}")
                                    }
                                </div>
                            </div>
                            
                            <h3><a href="@Url.Action("Detail", "Listing", new { area = "Customer", id = listing.Id })">@listing.ListingName</a></h3>
                            <div class="location">
                                <a href="@Url.Action("LocationDetail", "Listing", new { area = "Customer", id = listing.ListingLocationId })"><i class="fas fa-map-marker-alt"></i> @listing.ListingLocation.ListingLocationName</a>
                            </div>

                            @{
                                var listingReviews = Model.Reviews.Where(r => r.ListingId == listing.Id).ToList();
                                var reviewCount = listingReviews.Count;
                                var overallRating = 0.0;

                                if (reviewCount > 0)
                                {
                                    var totalRating = listingReviews.Sum(r => r.Rating);
                                    overallRating = (double)totalRating / reviewCount;

                                    // Round to nearest 0.5
                                    if (overallRating > 0 && overallRating <= 1)
                                        overallRating = 1;
                                    else if (overallRating > 1 && overallRating <= 1.5)
                                        overallRating = 1.5;
                                    else if (overallRating > 1.5 && overallRating <= 2)
                                        overallRating = 2;
                                    else if (overallRating > 2 && overallRating <= 2.5)
                                        overallRating = 2.5;
                                    else if (overallRating > 2.5 && overallRating <= 3)
                                        overallRating = 3;
                                    else if (overallRating > 3 && overallRating <= 3.5)
                                        overallRating = 3.5;
                                    else if (overallRating > 3.5 && overallRating <= 4)
                                        overallRating = 4;
                                    else if (overallRating > 4 && overallRating <= 4.5)
                                        overallRating = 4.5;
                                    else if (overallRating > 4.5 && overallRating <= 5)
                                        overallRating = 5;
                                }
                            }

                            <div class="review">
                                @if (overallRating == 5)
                                {
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                }
                                else if (overallRating == 4.5)
                                {
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                }
                                else if (overallRating == 4)
                                {
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                }
                                else if (overallRating == 3.5)
                                {
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                    <i class="far fa-star"></i>
                                }
                                else if (overallRating == 3)
                                {
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                }
                                else if (overallRating == 2.5)
                                {
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                }
                                else if (overallRating == 2)
                                {
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                }
                                else if (overallRating == 1.5)
                                {
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                }
                                else if (overallRating == 1)
                                {
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                }
                                else
                                {
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                }
                                <span>(@reviewCount @ViewBag.Reviews)</span>
                            </div>

                        </div>
                    </div>
                </div>
            }

        </div>
    </div>
</div>
